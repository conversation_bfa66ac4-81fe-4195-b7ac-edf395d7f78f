# Ryman Healthcare Contact Form - Cypress Test Suite Summary

## Overview
A comprehensive Cypress test suite has been created to test the "Contact us with your enquiry" form on the Ryman Healthcare website (https://www.rymanhealthcare.co.nz/contact-us) with a focus on **negative test cases for incomplete inputs**.

## 🎯 Test Objectives
- Validate form behavior with incomplete or invalid data
- Ensure proper validation error handling
- Test edge cases and boundary conditions
- Verify accessibility and cross-browser compatibility
- Prevent security vulnerabilities (XSS, injection attacks)

## 📁 Project Structure
```
├── cypress/
│   ├── e2e/
│   │   ├── contact-form-negative-tests.cy.js     # Main negative test cases
│   │   ├── contact-form-field-validation.cy.js   # Field-specific validation
│   │   └── contact-form-edge-cases.cy.js         # Edge cases & boundaries
│   ├── fixtures/
│   │   └── testData.json                         # Test data and scenarios
│   ├── support/
│   │   ├── commands.js                           # Custom Cypress commands
│   │   └── e2e.js                               # Support file
│   └── README.md                                 # Detailed test documentation
├── cypress.config.js                             # Cypress configuration
├── run-contact-form-tests.sh                     # Test runner script
└── CYPRESS_TEST_SUMMARY.md                       # This summary
```

## 🧪 Test Categories

### 1. Negative Test Cases (`contact-form-negative-tests.cy.js`)
- **Empty Form Submission**: Validates required field enforcement
- **Invalid Email Formats**: Tests malformed email addresses
- **Invalid Phone Numbers**: Tests incorrect phone number formats
- **Partial Form Completion**: Tests various incomplete form scenarios
- **Missing Enquiry Type**: Validates dropdown selection requirement
- **Field Length Validation**: Tests boundary conditions and XSS prevention

### 2. Field Validation Tests (`contact-form-field-validation.cy.js`)
- **Individual Field Validation**: Real-time validation testing
- **Cross-Field Validation**: Multi-field dependency testing
- **Form State Management**: Data preservation during validation
- **Accessibility Compliance**: ARIA labels and screen reader support

### 3. Edge Cases & Boundary Tests (`contact-form-edge-cases.cy.js`)
- **Boundary Value Testing**: Min/max length inputs
- **Special Character Handling**: Unicode, symbols, XSS attempts
- **Network Conditions**: Slow connections, server errors
- **Browser Compatibility**: Autofill, JavaScript fallbacks
- **Responsive Design**: Mobile and tablet viewports
- **Session Management**: Page refresh, navigation scenarios

## 🚀 Quick Start

### Install Dependencies
```bash
npm install
```

### Run Tests
```bash
# Interactive mode (recommended for development)
npm run cypress:open

# All contact form tests
npm run test:contact-form

# Specific test suites
npm run test:negative      # Negative test cases only
npm run test:validation    # Field validation tests only
npm run test:edge-cases    # Edge cases and boundary tests only

# Using the test runner script
./run-contact-form-tests.sh
```

## 📊 Test Scenarios Covered

### Incomplete Input Scenarios
1. **Completely Empty Form**: No fields filled
2. **Single Field Only**: Only one required field filled
3. **Missing Email**: All fields except email
4. **Missing Phone**: All fields except phone number
5. **Missing Name**: Email and phone only
6. **Missing Enquiry Type**: All fields except dropdown selection

### Invalid Data Scenarios
1. **Invalid Email Formats**:
   - `invalid-email`
   - `@example.com`
   - `test@`
   - `test.example.com`
   - `test@.com`

2. **Invalid Phone Numbers**:
   - `abc123`
   - `12345`
   - `++64123456789`
   - `phone-number`

3. **Malicious Input**:
   - XSS attempts: `<script>alert("test")</script>`
   - SQL injection patterns
   - HTML injection attempts

### Edge Cases
1. **Boundary Values**:
   - Minimum length inputs (1 character)
   - Maximum length inputs (255+ characters)
   - Empty spaces in required fields

2. **Special Characters**:
   - Unicode characters (José, Müller)
   - Special symbols (!@#$%^&*)
   - HTML entities

3. **Network Conditions**:
   - Slow network simulation
   - Server error responses (500, 404)
   - Connection timeouts

## 🔧 Custom Commands

The test suite includes reusable Cypress commands:

```javascript
cy.visitContactForm()                    // Navigate to contact form
cy.fillFormPartially(formData)          // Fill form with partial data
cy.attemptFormSubmission()              // Try to submit form
cy.checkValidationError(selector, msg)  // Verify validation errors
```

## 📈 Expected Test Results

### Tests Should PASS When:
- Form prevents submission with incomplete data
- Validation errors are displayed appropriately
- Invalid email/phone formats are rejected
- XSS attempts are sanitized
- Form maintains state during validation
- Accessibility requirements are met

### Tests Should FAIL If:
- Form allows submission with missing required fields
- Invalid data is accepted without validation
- XSS scripts execute successfully
- Form loses data during validation
- Accessibility attributes are missing

## 🛠️ Configuration

### Cypress Configuration (`cypress.config.js`)
- **Base URL**: `https://www.rymanhealthcare.co.nz`
- **Viewport**: 1280x720 (with responsive tests)
- **Timeouts**: 10 seconds for commands/requests
- **Video Recording**: Enabled for test runs
- **Screenshots**: Captured on failures

### Test Data (`cypress/fixtures/testData.json`)
- Valid form data examples
- Invalid input collections
- Partial completion scenarios
- Expected error messages

## 🔍 Debugging and Maintenance

### When Tests Fail:
1. Check `cypress/screenshots/` for failure images
2. Review `cypress/videos/` for test recordings
3. Examine console output for detailed errors
4. Use `cy.debug()` or `cy.pause()` for interactive debugging

### Updating Tests:
1. Modify selectors in `cypress/support/commands.js` if form structure changes
2. Update test data in `cypress/fixtures/testData.json`
3. Add new test cases following existing patterns

## 📱 Browser and Device Support

### Tested Browsers:
- Chrome (primary)
- Firefox
- Edge
- Safari (with limitations)

### Tested Viewports:
- Desktop: 1280x720
- Tablet: iPad dimensions
- Mobile: iPhone X dimensions

## 🎯 Success Criteria

The test suite validates that the Ryman Healthcare contact form:
1. ✅ Enforces all required field validation
2. ✅ Prevents submission with incomplete data
3. ✅ Validates email and phone number formats
4. ✅ Handles edge cases gracefully
5. ✅ Maintains accessibility standards
6. ✅ Prevents security vulnerabilities
7. ✅ Works across different devices and browsers

## 📞 Support

For questions or issues with the test suite:
1. Review the detailed documentation in `cypress/README.md`
2. Check Cypress official documentation: https://docs.cypress.io
3. Examine test output and error messages for specific failures
