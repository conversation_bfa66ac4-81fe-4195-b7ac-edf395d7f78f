#!/usr/bin/env python3
"""
Static Resource Fixer
This script fixes CSS and JavaScript references in HTML files to ensure they work locally
"""

import os
import re
import requests
import time
from pathlib import Path
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import logging
from config import *


class StaticResourceFixer:
    def __init__(self, website_dir='ryman_website_simple'):
        self.website_dir = Path(website_dir)
        self.base_url = BASE_URL
        
        # Setup session
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': USER_AGENT})
        
        # Tracking
        self.fixed_resources = 0
        self.downloaded_resources = 0
        self.external_resources = set()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.website_dir / 'static_fixer.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def fix_all_static_resources(self):
        """Fix all static resource references in HTML files"""
        self.logger.info("Starting static resource fixing process...")
        
        html_files = list(self.website_dir.rglob('*.html'))
        self.logger.info(f"Found {len(html_files)} HTML files to process")
        
        for html_file in html_files:
            self.fix_static_resources_in_file(html_file)
        
        # Download any missing external resources
        self.download_missing_external_resources()
        
        self.logger.info(f"Static resource fixing completed!")
        self.logger.info(f"Fixed {self.fixed_resources} resource references")
        self.logger.info(f"Downloaded {self.downloaded_resources} external resources")
    
    def fix_static_resources_in_file(self, html_file):
        """Fix static resource references in a single HTML file"""
        try:
            with open(html_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            original_content = content
            
            # Fix malformed paths like "index.html/hs-fs/..."
            content = re.sub(r'(href|src)="index\.html/(hs-fs/[^"]+)"', r'\1="\2"', content)
            
            # Fix double slashes in paths
            content = re.sub(r'(href|src)="([^"]*?)//([^"]*?)"', r'\1="\2/\3"', content)
            
            # Find and fix external CDN resources
            content = self.fix_external_cdn_resources(content, html_file)
            
            # Fix relative path issues
            content = self.fix_relative_paths(content, html_file)
            
            # Save if content changed
            if content != original_content:
                with open(html_file, 'w', encoding='utf-8', errors='ignore') as f:
                    f.write(content)
                self.logger.info(f"Fixed static resources in: {html_file}")
                self.fixed_resources += 1
                
        except Exception as e:
            self.logger.error(f"Error fixing static resources in {html_file}: {str(e)}")
    
    def fix_external_cdn_resources(self, content, html_file):
        """Fix external CDN resources by downloading them locally"""
        
        # Common external resources to download
        external_patterns = [
            (r'https://static\.hsappstatic\.net/[^"\']+', 'static/hsappstatic/'),
            (r'https://code\.jquery\.com/[^"\']+', 'static/jquery/'),
            (r'https://cdn\.jsdelivr\.net/[^"\']+', 'static/jsdelivr/'),
            (r'https://[^"\']*\.hubspotusercontent[^"\']*\.net/[^"\']+', 'static/hubspot/'),
            (r'https://www\.googletagmanager\.com/[^"\']+', 'static/google/'),
            (r'https://no-cache\.hubspot\.com/[^"\']+', 'static/hubspot-nocache/')
        ]
        
        for pattern, local_dir in external_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                local_path = self.download_external_resource(match, local_dir)
                if local_path:
                    # Calculate relative path from current HTML file to the downloaded resource
                    relative_path = os.path.relpath(local_path, html_file.parent)
                    relative_path = relative_path.replace('\\', '/')
                    content = content.replace(match, relative_path)
                    self.logger.info(f"Replaced external resource: {match} -> {relative_path}")
        
        return content
    
    def download_external_resource(self, url, local_dir):
        """Download an external resource to local directory"""
        try:
            # Create local path
            parsed_url = urlparse(url)
            filename = os.path.basename(parsed_url.path) or 'index.html'
            if not filename or '.' not in filename:
                filename = 'resource.js' if 'js' in url else 'resource.css'
            
            local_path = self.website_dir / local_dir / filename
            
            # Skip if already exists
            if local_path.exists():
                return local_path
            
            # Download the resource
            self.logger.info(f"Downloading external resource: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Create directory and save
            local_path.parent.mkdir(parents=True, exist_ok=True)
            
            if 'text/' in response.headers.get('content-type', '') or url.endswith(('.js', '.css')):
                with open(local_path, 'w', encoding='utf-8', errors='ignore') as f:
                    f.write(response.text)
            else:
                with open(local_path, 'wb') as f:
                    f.write(response.content)
            
            self.downloaded_resources += 1
            return local_path
            
        except Exception as e:
            self.logger.error(f"Failed to download external resource {url}: {str(e)}")
            return None
    
    def fix_relative_paths(self, content, html_file):
        """Fix relative path issues in CSS and JS references"""
        
        # Fix paths that start with wrong directory references
        content = re.sub(r'(href|src)="([^"]*?)/hs-fs/', r'\1="hs-fs/', content)
        
        # Fix paths that have extra slashes
        content = re.sub(r'(href|src)="([^"]*?)//+([^"]*?)"', r'\1="\2/\3"', content)
        
        return content
    
    def download_missing_external_resources(self):
        """Download any remaining external resources found in the files"""
        if self.external_resources:
            self.logger.info(f"Downloading {len(self.external_resources)} additional external resources...")
            for url in self.external_resources:
                self.download_external_resource(url, 'static/misc/')
    
    def verify_static_resources(self):
        """Verify that all static resources are accessible"""
        self.logger.info("Verifying static resource accessibility...")
        
        html_files = list(self.website_dir.rglob('*.html'))
        missing_resources = []
        total_resources = 0
        
        for html_file in html_files:
            try:
                with open(html_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                soup = BeautifulSoup(content, 'html.parser')
                
                # Check CSS files
                for link in soup.find_all('link', rel='stylesheet'):
                    href = link.get('href')
                    if href and not href.startswith(('http', '//')):
                        total_resources += 1
                        resource_path = html_file.parent / href
                        if not resource_path.exists():
                            missing_resources.append(f"{html_file}: {href}")
                
                # Check JS files
                for script in soup.find_all('script', src=True):
                    src = script.get('src')
                    if src and not src.startswith(('http', '//')):
                        total_resources += 1
                        resource_path = html_file.parent / src
                        if not resource_path.exists():
                            missing_resources.append(f"{html_file}: {src}")
                            
            except Exception as e:
                self.logger.error(f"Error verifying resources in {html_file}: {str(e)}")
        
        self.logger.info(f"Verification complete:")
        self.logger.info(f"Total local resources checked: {total_resources}")
        self.logger.info(f"Missing resources: {len(missing_resources)}")
        
        if missing_resources:
            self.logger.warning("Missing resources found:")
            for missing in missing_resources[:10]:  # Show first 10
                self.logger.warning(f"  {missing}")
            if len(missing_resources) > 10:
                self.logger.warning(f"  ... and {len(missing_resources) - 10} more")
        else:
            self.logger.info("✅ All local static resources are accessible!")
        
        return len(missing_resources) == 0
    
    def create_resource_summary(self):
        """Create a summary of all static resources"""
        css_files = list(self.website_dir.rglob('*.css'))
        js_files = list(self.website_dir.rglob('*.js'))
        
        summary = f"""
# Static Resources Summary

## CSS Files: {len(css_files)}
{chr(10).join([f"- {f.relative_to(self.website_dir)}" for f in css_files[:10]])}
{'...' if len(css_files) > 10 else ''}

## JavaScript Files: {len(js_files)}
{chr(10).join([f"- {f.relative_to(self.website_dir)}" for f in js_files[:10]])}
{'...' if len(js_files) > 10 else ''}

## Statistics
- Total CSS files: {len(css_files)}
- Total JS files: {len(js_files)}
- Fixed resource references: {self.fixed_resources}
- Downloaded external resources: {self.downloaded_resources}
"""
        
        with open(self.website_dir / 'static_resources_summary.md', 'w') as f:
            f.write(summary)
        
        self.logger.info("Created static resources summary")


def main():
    fixer = StaticResourceFixer()
    
    # Fix all static resources
    fixer.fix_all_static_resources()
    
    # Verify resources
    fixer.verify_static_resources()
    
    # Create summary
    fixer.create_resource_summary()
    
    print("\n🎉 Static resource fixing completed!")
    print(f"Fixed {fixer.fixed_resources} resource references")
    print(f"Downloaded {fixer.downloaded_resources} external resources")


if __name__ == '__main__':
    main()
