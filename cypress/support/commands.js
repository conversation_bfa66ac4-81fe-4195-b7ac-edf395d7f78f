// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Custom command to navigate to contact form
Cypress.Commands.add('visitContactForm', () => {
  cy.visit('/contact-us?hsCtaTracking=0d478680-803f-47ee-ba44-8f08f5539fb4%7Ca9f3e1bd-9b32-4579-9fd1-28ea95b44dce')
  cy.wait(2000) // Wait for page to load
})

// Custom command to check for form validation errors
Cypress.Commands.add('checkValidationError', (fieldSelector, expectedError) => {
  cy.get(fieldSelector).should('be.visible')
  if (expectedError) {
    cy.contains(expectedError).should('be.visible')
  }
})

// Custom command to fill form partially
Cypress.Commands.add('fillFormPartially', (formData) => {
  if (formData.firstName) {
    cy.get('body').then(($body) => {
      const selectors = [
        '[data-cy="first-name"]',
        'input[name*="firstname"]',
        'input[name*="first"]',
        'input[placeholder*="First"]',
        'input[id*="first"]',
        'input[name*="fname"]',
        'input[class*="first"]'
      ]

      for (let selector of selectors) {
        if ($body.find(selector).length > 0) {
          cy.get(selector).first().clear().type(formData.firstName)
          break
        }
      }
    })
  }

  if (formData.lastName) {
    cy.get('body').then(($body) => {
      const selectors = [
        '[data-cy="last-name"]',
        'input[name*="lastname"]',
        'input[name*="last"]',
        'input[placeholder*="Last"]',
        'input[id*="last"]',
        'input[name*="lname"]',
        'input[class*="last"]'
      ]

      for (let selector of selectors) {
        if ($body.find(selector).length > 0) {
          cy.get(selector).first().clear().type(formData.lastName)
          break
        }
      }
    })
  }

  if (formData.email) {
    cy.get('body').then(($body) => {
      const selectors = [
        '[data-cy="email"]',
        'input[type="email"]',
        'input[name*="email"]',
        'input[placeholder*="email"]',
        'input[id*="email"]'
      ]

      for (let selector of selectors) {
        if ($body.find(selector).length > 0) {
          cy.get(selector).first().clear().type(formData.email)
          break
        }
      }
    })
  }

  if (formData.phone) {
    cy.get('body').then(($body) => {
      const selectors = [
        '[data-cy="phone"]',
        'input[type="tel"]',
        'input[name*="phone"]',
        'input[placeholder*="phone"]',
        'input[id*="phone"]',
        'input[name*="mobile"]'
      ]

      for (let selector of selectors) {
        if ($body.find(selector).length > 0) {
          cy.get(selector).first().clear().type(formData.phone)
          break
        }
      }
    })
  }

  if (formData.enquiryType) {
    cy.get('body').then(($body) => {
      const selectors = [
        '[data-cy="enquiry-type"]',
        'select[name*="enquiry"]',
        'select[name*="type"]',
        'select[id*="enquiry"]',
        'select[class*="enquiry"]'
      ]

      for (let selector of selectors) {
        if ($body.find(selector).length > 0) {
          cy.get(selector).first().select(formData.enquiryType)
          break
        }
      }
    })
  }

  if (formData.message) {
    cy.get('body').then(($body) => {
      const selectors = [
        '[data-cy="message"]',
        'textarea',
        'input[name*="message"]',
        'input[name*="comment"]',
        'textarea[name*="message"]',
        'textarea[placeholder*="message"]'
      ]

      for (let selector of selectors) {
        if ($body.find(selector).length > 0) {
          cy.get(selector).first().clear().type(formData.message)
          break
        }
      }
    })
  }
})

// Custom command to attempt form submission
Cypress.Commands.add('attemptFormSubmission', () => {
  cy.get('body').then(($body) => {
    const selectors = [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      'button:contains("Send")',
      'button:contains("Enquire")',
      'button:contains("Contact")',
      '[data-cy="submit"]',
      '.submit-button',
      '#submit-button'
    ]

    for (let selector of selectors) {
      if ($body.find(selector).length > 0) {
        cy.get(selector).first().click()
        break
      }
    }
  })
})
