// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Custom command to navigate to contact form
Cypress.Commands.add('visitContactForm', () => {
  cy.visit('/contact-us?hsCtaTracking=0d478680-803f-47ee-ba44-8f08f5539fb4%7Ca9f3e1bd-9b32-4579-9fd1-28ea95b44dce')
  cy.wait(2000) // Wait for page to load
})

// Custom command to check for form validation errors
Cypress.Commands.add('checkValidationError', (fieldSelector, expectedError) => {
  cy.get(fieldSelector).should('be.visible')
  if (expectedError) {
    cy.contains(expectedError).should('be.visible')
  }
})

// Custom command to fill form partially
Cypress.Commands.add('fillFormPartially', (formData) => {
  if (formData.firstName) {
    cy.get('[data-cy="first-name"], input[name*="firstname"], input[placeholder*="First"], input[id*="first"]').first().type(formData.firstName)
  }
  if (formData.lastName) {
    cy.get('[data-cy="last-name"], input[name*="lastname"], input[placeholder*="Last"], input[id*="last"]').first().type(formData.lastName)
  }
  if (formData.email) {
    cy.get('[data-cy="email"], input[type="email"], input[name*="email"], input[placeholder*="email"]').first().type(formData.email)
  }
  if (formData.phone) {
    cy.get('[data-cy="phone"], input[type="tel"], input[name*="phone"], input[placeholder*="phone"]').first().type(formData.phone)
  }
  if (formData.enquiryType) {
    cy.get('[data-cy="enquiry-type"], select[name*="enquiry"], select[name*="type"]').first().select(formData.enquiryType)
  }
  if (formData.message) {
    cy.get('[data-cy="message"], textarea, input[name*="message"], input[name*="comment"]').first().type(formData.message)
  }
})

// Custom command to attempt form submission
Cypress.Commands.add('attemptFormSubmission', () => {
  cy.get('button[type="submit"], input[type="submit"], button:contains("Submit"), button:contains("Send")').first().click()
})
