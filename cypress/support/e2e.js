// ***********************************************************
// This example support/e2e.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Handle uncaught exceptions from the application
Cypress.on('uncaught:exception', (err, runnable) => {
  // Ignore specific errors that come from the website's third-party libraries
  if (err.message.includes('lazyloadanything is not a function')) {
    return false
  }
  if (err.message.includes('$ is not defined')) {
    return false
  }
  if (err.message.includes('jQ<PERSON>y is not defined')) {
    return false
  }
  // Let other errors fail the test
  return true
})

// Alternatively you can use CommonJS syntax:
// require('./commands')
