describe('Ryman Healthcare Contact Form - Form Inspection', () => {
  it('should inspect the contact form structure', () => {
    cy.visitContactForm()

    // Wait for page to fully load
    cy.wait(3000)

    // Check if there's a contact form visible
    cy.get('body').then(($body) => {
      console.log('=== FORM INSPECTION RESULTS ===')

      // Look for forms
      const forms = $body.find('form')
      console.log(`Found ${forms.length} form(s) on the page`)

      // Look for all input fields
      const inputs = $body.find('input')
      console.log(`Found ${inputs.length} input field(s)`)
      inputs.each((index, input) => {
        const name = input.name || 'no-name'
        const type = input.type || 'no-type'
        const id = input.id || 'no-id'
        const placeholder = input.placeholder || 'no-placeholder'
        const className = input.className || 'no-class'
        console.log(`Input ${index + 1}: name="${name}", type="${type}", id="${id}", placeholder="${placeholder}", class="${className}"`)
      })

      // Look for select fields
      const selects = $body.find('select')
      console.log(`Found ${selects.length} select field(s)`)
      selects.each((index, select) => {
        const name = select.name || 'no-name'
        const id = select.id || 'no-id'
        const className = select.className || 'no-class'
        console.log(`Select ${index + 1}: name="${name}", id="${id}", class="${className}"`)
      })

      // Look for textarea fields
      const textareas = $body.find('textarea')
      console.log(`Found ${textareas.length} textarea field(s)`)
      textareas.each((index, textarea) => {
        const name = textarea.name || 'no-name'
        const id = textarea.id || 'no-id'
        const placeholder = textarea.placeholder || 'no-placeholder'
        const className = textarea.className || 'no-class'
        console.log(`Textarea ${index + 1}: name="${name}", id="${id}", placeholder="${placeholder}", class="${className}"`)
      })

      // Look for buttons
      const buttons = $body.find('button')
      console.log(`Found ${buttons.length} button(s)`)
      buttons.each((index, button) => {
        const type = button.type || 'no-type'
        const text = button.textContent || 'no-text'
        const id = button.id || 'no-id'
        const className = button.className || 'no-class'
        console.log(`Button ${index + 1}: type="${type}", text="${text.trim()}", id="${id}", class="${className}"`)
      })

      // Look for any HubSpot forms (common on marketing sites)
      const hubspotForms = $body.find('[class*="hs-form"], [id*="hsForm"], .hbspt-form')
      console.log(`Found ${hubspotForms.length} HubSpot form(s)`)

      // Look for any iframe forms
      const iframes = $body.find('iframe')
      console.log(`Found ${iframes.length} iframe(s)`)
      iframes.each((index, iframe) => {
        const src = iframe.src || 'no-src'
        const id = iframe.id || 'no-id'
        console.log(`Iframe ${index + 1}: src="${src}", id="${id}"`)
      })

      console.log('=== END FORM INSPECTION ===')
    })

    // Take a screenshot for manual inspection
    cy.screenshot('contact-form-structure')

    // Scroll down to see if there are forms below the fold
    cy.scrollTo('bottom')
    cy.wait(2000)
    cy.screenshot('contact-form-bottom')
  })
})
