describe('Ryman Healthcare Contact Form - Negative Test Cases', () => {
  let testData

  before(() => {
    cy.fixture('testData').then((data) => {
      testData = data
    })
  })

  beforeEach(() => {
    cy.visitContactForm()
  })

  describe('Empty Form Submission', () => {
    it('should prevent submission when all fields are empty', () => {
      cy.attemptFormSubmission()
      
      // Check that form was not submitted and validation errors appear
      cy.url().should('include', '/contact-us')
      
      // Look for common validation error indicators
      cy.get('body').should('contain.text', 'required').or('contain.text', 'Please')
    })

    it('should show validation errors for required fields', () => {
      cy.attemptFormSubmission()
      
      // Check for validation messages on required fields
      cy.get('input, select, textarea').each(($el) => {
        const isRequired = $el.attr('required') !== undefined || 
                          $el.attr('aria-required') === 'true' ||
                          $el.hasClass('required')
        
        if (isRequired) {
          cy.wrap($el).should('have.attr', 'aria-invalid', 'true')
            .or('have.class', 'error')
            .or('have.class', 'invalid')
        }
      })
    })
  })

  describe('Invalid Email Format Tests', () => {
    testData?.invalidEmails?.forEach((invalidEmail, index) => {
      it(`should reject invalid email format: "${invalidEmail}"`, () => {
        if (invalidEmail) {
          cy.fillFormPartially({
            firstName: 'John',
            lastName: 'Doe',
            email: invalidEmail,
            phone: '021234567'
          })
        } else {
          cy.fillFormPartially({
            firstName: 'John',
            lastName: 'Doe',
            phone: '021234567'
          })
        }
        
        cy.attemptFormSubmission()
        
        // Check that form was not submitted
        cy.url().should('include', '/contact-us')
        
        // Look for email validation error
        cy.get('input[type="email"], input[name*="email"]').should('have.attr', 'aria-invalid', 'true')
          .or('have.class', 'error')
          .or('have.class', 'invalid')
      })
    })
  })

  describe('Invalid Phone Number Tests', () => {
    testData?.invalidPhones?.forEach((invalidPhone, index) => {
      it(`should reject invalid phone format: "${invalidPhone}"`, () => {
        if (invalidPhone) {
          cy.fillFormPartially({
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: invalidPhone
          })
        } else {
          cy.fillFormPartially({
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>'
          })
        }
        
        cy.attemptFormSubmission()
        
        // Check that form was not submitted
        cy.url().should('include', '/contact-us')
        
        // Look for phone validation error
        cy.get('input[type="tel"], input[name*="phone"]').should('have.attr', 'aria-invalid', 'true')
          .or('have.class', 'error')
          .or('have.class', 'invalid')
      })
    })
  })

  describe('Partial Form Completion Tests', () => {
    it('should prevent submission with only first name', () => {
      cy.fillFormPartially(testData.partialFormData.onlyFirstName)
      cy.attemptFormSubmission()
      
      cy.url().should('include', '/contact-us')
      cy.get('body').should('contain.text', 'required').or('contain.text', 'Please')
    })

    it('should prevent submission with only email', () => {
      cy.fillFormPartially(testData.partialFormData.onlyEmail)
      cy.attemptFormSubmission()
      
      cy.url().should('include', '/contact-us')
      cy.get('body').should('contain.text', 'required').or('contain.text', 'Please')
    })

    it('should prevent submission with only phone', () => {
      cy.fillFormPartially(testData.partialFormData.onlyPhone)
      cy.attemptFormSubmission()
      
      cy.url().should('include', '/contact-us')
      cy.get('body').should('contain.text', 'required').or('contain.text', 'Please')
    })

    it('should prevent submission with name and email only (missing phone)', () => {
      cy.fillFormPartially(testData.partialFormData.nameAndEmail)
      cy.attemptFormSubmission()
      
      cy.url().should('include', '/contact-us')
      cy.get('body').should('contain.text', 'required').or('contain.text', 'Please')
    })

    it('should prevent submission with name and phone only (missing email)', () => {
      cy.fillFormPartially(testData.partialFormData.nameAndPhone)
      cy.attemptFormSubmission()
      
      cy.url().should('include', '/contact-us')
      cy.get('body').should('contain.text', 'required').or('contain.text', 'Please')
    })

    it('should prevent submission with email and phone only (missing name)', () => {
      cy.fillFormPartially(testData.partialFormData.emailAndPhone)
      cy.attemptFormSubmission()
      
      cy.url().should('include', '/contact-us')
      cy.get('body').should('contain.text', 'required').or('contain.text', 'Please')
    })
  })

  describe('Missing Enquiry Type Tests', () => {
    it('should prevent submission when enquiry type is not selected', () => {
      cy.fillFormPartially({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '021234567',
        message: 'Test message'
      })
      
      cy.attemptFormSubmission()
      
      cy.url().should('include', '/contact-us')
      cy.get('select[name*="enquiry"], select[name*="type"]').should('have.attr', 'aria-invalid', 'true')
        .or('have.class', 'error')
        .or('have.class', 'invalid')
    })
  })

  describe('Field Length Validation Tests', () => {
    it('should handle extremely long input values', () => {
      const longText = 'a'.repeat(1000)
      
      cy.fillFormPartially({
        firstName: longText,
        lastName: longText,
        email: '<EMAIL>',
        phone: '021234567',
        message: longText
      })
      
      cy.attemptFormSubmission()
      
      // Check if form handles long input gracefully
      cy.url().should('include', '/contact-us')
    })

    it('should handle special characters in input fields', () => {
      cy.fillFormPartially({
        firstName: '<script>alert("test")</script>',
        lastName: '!@#$%^&*()',
        email: '<EMAIL>',
        phone: '021234567',
        message: 'Test with special chars: <>&"\''
      })
      
      cy.attemptFormSubmission()
      
      // Ensure no script execution or errors
      cy.url().should('include', '/contact-us')
    })
  })
})
