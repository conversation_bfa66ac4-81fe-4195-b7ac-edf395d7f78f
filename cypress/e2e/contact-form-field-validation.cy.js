describe('Ryman Healthcare Contact Form - Field Validation Tests', () => {
  let testData

  before(() => {
    cy.fixture('testData').then((data) => {
      testData = data
    })
  })

  beforeEach(() => {
    cy.visitContactForm()
  })

  describe('Individual Field Validation', () => {
    it('should validate first name field is required', () => {
      // Focus and blur first name field without entering data
      cy.get('input[name*="firstname"], input[placeholder*="First"], input[id*="first"]')
        .first()
        .focus()
        .blur()
      
      // Check for validation error
      cy.get('body').should('contain.text', 'required')
        .or('contain.text', 'Please enter')
        .or('contain.text', 'This field')
    })

    it('should validate last name field is required', () => {
      cy.get('input[name*="lastname"], input[placeholder*="Last"], input[id*="last"]')
        .first()
        .focus()
        .blur()
      
      cy.get('body').should('contain.text', 'required')
        .or('contain.text', 'Please enter')
        .or('contain.text', 'This field')
    })

    it('should validate email field format in real-time', () => {
      const emailField = cy.get('input[type="email"], input[name*="email"]').first()
      
      // Test invalid email formats
      testData.invalidEmails.forEach((invalidEmail) => {
        if (invalidEmail) {
          emailField.clear().type(invalidEmail).blur()
          
          cy.get('body').should('contain.text', 'valid email')
            .or('contain.text', 'invalid')
            .or('contain.text', 'format')
        }
      })
    })

    it('should validate phone field format', () => {
      const phoneField = cy.get('input[type="tel"], input[name*="phone"]').first()
      
      testData.invalidPhones.forEach((invalidPhone) => {
        if (invalidPhone) {
          phoneField.clear().type(invalidPhone).blur()
          
          // Check for phone validation (may vary by implementation)
          cy.get('body').should('contain.text', 'valid phone')
            .or('contain.text', 'invalid')
            .or('contain.text', 'number')
        }
      })
    })

    it('should validate enquiry type selection is required', () => {
      cy.get('select[name*="enquiry"], select[name*="type"]')
        .first()
        .focus()
        .blur()
      
      cy.get('body').should('contain.text', 'select')
        .or('contain.text', 'choose')
        .or('contain.text', 'required')
    })
  })

  describe('Cross-Field Validation', () => {
    it('should require all mandatory fields before allowing submission', () => {
      // Fill all but one required field
      cy.fillFormPartially({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
        // Missing phone and enquiry type
      })
      
      cy.attemptFormSubmission()
      
      // Should still be on contact page with errors
      cy.url().should('include', '/contact-us')
      cy.get('body').should('contain.text', 'required')
        .or('contain.text', 'Please')
        .or('contain.text', 'complete')
    })

    it('should validate email uniqueness or format consistency', () => {
      cy.fillFormPartially({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>', // Test case sensitivity
        phone: '021234567'
      })
      
      cy.attemptFormSubmission()
      
      // Check if form handles email case sensitivity
      cy.url().should('include', '/contact-us')
    })
  })

  describe('Form State Validation', () => {
    it('should maintain form state after validation errors', () => {
      const formData = {
        firstName: 'John',
        lastName: 'Doe',
        email: 'invalid-email',
        phone: '021234567'
      }
      
      cy.fillFormPartially(formData)
      cy.attemptFormSubmission()
      
      // Check that valid data is preserved
      cy.get('input[name*="firstname"]').should('have.value', formData.firstName)
      cy.get('input[name*="lastname"]').should('have.value', formData.lastName)
      cy.get('input[name*="phone"]').should('have.value', formData.phone)
    })

    it('should clear validation errors when field is corrected', () => {
      // Enter invalid email
      cy.get('input[type="email"]').first().type('invalid-email').blur()
      
      // Check error appears
      cy.get('body').should('contain.text', 'valid email')
        .or('contain.text', 'invalid')
      
      // Correct the email
      cy.get('input[type="email"]').first().clear().type('<EMAIL>').blur()
      
      // Error should be cleared (this test may need adjustment based on actual implementation)
      cy.wait(1000) // Allow time for validation to clear
    })

    it('should prevent multiple form submissions', () => {
      cy.fillFormPartially({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '021234567'
      })
      
      // Try to submit multiple times quickly
      cy.get('button[type="submit"]').first().click()
      cy.get('button[type="submit"]').first().click()
      cy.get('button[type="submit"]').first().click()
      
      // Should handle multiple clicks gracefully
      cy.url().should('include', '/contact-us')
    })
  })

  describe('Accessibility Validation', () => {
    it('should have proper ARIA labels for form fields', () => {
      cy.get('input, select, textarea').each(($el) => {
        // Check for accessibility attributes
        cy.wrap($el).should('satisfy', (el) => {
          return el.attr('aria-label') || 
                 el.attr('aria-labelledby') || 
                 el.attr('placeholder') ||
                 el.closest('label').length > 0
        })
      })
    })

    it('should indicate required fields to screen readers', () => {
      cy.get('input[required], select[required], textarea[required]').each(($el) => {
        cy.wrap($el).should('satisfy', (el) => {
          return el.attr('aria-required') === 'true' || 
                 el.attr('required') !== undefined ||
                 el.closest('label').text().includes('*')
        })
      })
    })

    it('should provide error messages that are accessible', () => {
      cy.attemptFormSubmission()
      
      // Check that error messages are associated with form fields
      cy.get('[aria-invalid="true"]').each(($el) => {
        cy.wrap($el).should('satisfy', (el) => {
          return el.attr('aria-describedby') || 
                 el.closest('.form-group, .field').find('.error, .invalid').length > 0
        })
      })
    })
  })
})
