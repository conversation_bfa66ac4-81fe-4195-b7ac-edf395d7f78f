describe('Ryman Healthcare Contact Form - Working Negative Tests', () => {
  beforeEach(() => {
    cy.visit('https://www.rymanhealthcare.co.nz/contact-us?hsCtaTracking=0d478680-803f-47ee-ba44-8f08f5539fb4%7Ca9f3e1bd-9b32-4579-9fd1-28ea95b44dce')
    cy.wait(5000) // Wait for page to load completely
  })

  it('should find and test the contact form with negative cases', () => {
    // Look for forms that are likely to be contact forms (not search forms)
    cy.get('form').then(($forms) => {
      cy.log(`Found ${$forms.length} forms on the page`)
      
      // Try to find a form that contains contact-related fields
      let contactFormFound = false
      
      $forms.each((index, form) => {
        const $form = Cypress.$(form)
        
        // Look for typical contact form fields
        const hasEmailField = $form.find('input[type="email"], input[name*="email"]').length > 0
        const hasNameField = $form.find('input[name*="name"], input[name*="first"], input[name*="last"]').length > 0
        const hasPhoneField = $form.find('input[type="tel"], input[name*="phone"]').length > 0
        const hasMessageField = $form.find('textarea, input[name*="message"], input[name*="comment"]').length > 0
        
        // Skip search forms
        const isSearchForm = $form.find('input[name*="search"], input[name*="term"], input[name*="query"]').length > 0
        
        if ((hasEmailField || hasNameField || hasPhoneField || hasMessageField) && !isSearchForm) {
          contactFormFound = true
          cy.log(`Found potential contact form at index ${index}`)
          
          // Test this form
          cy.wrap($form).within(() => {
            // Test 1: Empty form submission
            cy.log('Testing empty form submission')
            cy.get('input[type="submit"], button[type="submit"], button:contains("Submit"), button:contains("Send")').first().click()
            
            // Should stay on the same page or show validation errors
            cy.wait(2000)
          })
          
          // Check if we're still on contact page (good) or if validation errors appeared
          cy.url().then((url) => {
            if (url.includes('/contact-us')) {
              cy.log('✅ Form prevented empty submission - stayed on contact page')
            } else {
              cy.log('⚠️ Form submitted despite being empty - redirected to: ' + url)
            }
          })
          
          // Test 2: Invalid email format
          cy.wrap($form).within(() => {
            cy.log('Testing invalid email format')
            
            // Clear any existing values and fill with invalid data
            cy.get('input[type="email"], input[name*="email"]').then(($emailInputs) => {
              if ($emailInputs.length > 0) {
                cy.wrap($emailInputs.first()).clear().type('invalid-email-format')
              }
            })
            
            // Fill other required fields with valid data to isolate email validation
            cy.get('input[name*="name"], input[name*="first"]').then(($nameInputs) => {
              if ($nameInputs.length > 0) {
                cy.wrap($nameInputs.first()).clear().type('John')
              }
            })
            
            cy.get('input[name*="last"]').then(($lastInputs) => {
              if ($lastInputs.length > 0) {
                cy.wrap($lastInputs.first()).clear().type('Doe')
              }
            })
            
            cy.get('input[type="tel"], input[name*="phone"]').then(($phoneInputs) => {
              if ($phoneInputs.length > 0) {
                cy.wrap($phoneInputs.first()).clear().type('021234567')
              }
            })
            
            // Try to submit
            cy.get('input[type="submit"], button[type="submit"], button:contains("Submit"), button:contains("Send")').first().click()
            cy.wait(2000)
          })
          
          // Check if form validation caught the invalid email
          cy.url().then((url) => {
            if (url.includes('/contact-us')) {
              cy.log('✅ Form caught invalid email - stayed on contact page')
            } else {
              cy.log('⚠️ Form accepted invalid email - redirected to: ' + url)
            }
          })
          
          // Test 3: Missing required fields (partial completion)
          cy.visit('https://www.rymanhealthcare.co.nz/contact-us?hsCtaTracking=0d478680-803f-47ee-ba44-8f08f5539fb4%7Ca9f3e1bd-9b32-4579-9fd1-28ea95b44dce')
          cy.wait(3000)
          
          cy.get('form').eq(index).within(() => {
            cy.log('Testing partial form completion')
            
            // Fill only email field, leave other required fields empty
            cy.get('input[type="email"], input[name*="email"]').then(($emailInputs) => {
              if ($emailInputs.length > 0) {
                cy.wrap($emailInputs.first()).clear().type('<EMAIL>')
              }
            })
            
            // Try to submit with only email filled
            cy.get('input[type="submit"], button[type="submit"], button:contains("Submit"), button:contains("Send")').first().click()
            cy.wait(2000)
          })
          
          // Check if form validation caught the missing fields
          cy.url().then((url) => {
            if (url.includes('/contact-us')) {
              cy.log('✅ Form caught missing required fields - stayed on contact page')
            } else {
              cy.log('⚠️ Form accepted partial data - redirected to: ' + url)
            }
          })
          
          // Test 4: Invalid phone number format
          cy.visit('https://www.rymanhealthcare.co.nz/contact-us?hsCtaTracking=0d478680-803f-47ee-ba44-8f08f5539fb4%7Ca9f3e1bd-9b32-4579-9fd1-28ea95b44dce')
          cy.wait(3000)
          
          cy.get('form').eq(index).within(() => {
            cy.log('Testing invalid phone number format')
            
            // Fill with valid data except phone
            cy.get('input[name*="name"], input[name*="first"]').then(($nameInputs) => {
              if ($nameInputs.length > 0) {
                cy.wrap($nameInputs.first()).clear().type('John')
              }
            })
            
            cy.get('input[type="email"], input[name*="email"]').then(($emailInputs) => {
              if ($emailInputs.length > 0) {
                cy.wrap($emailInputs.first()).clear().type('<EMAIL>')
              }
            })
            
            // Invalid phone number
            cy.get('input[type="tel"], input[name*="phone"]').then(($phoneInputs) => {
              if ($phoneInputs.length > 0) {
                cy.wrap($phoneInputs.first()).clear().type('abc123invalid')
              }
            })
            
            // Try to submit
            cy.get('input[type="submit"], button[type="submit"], button:contains("Submit"), button:contains("Send")').first().click()
            cy.wait(2000)
          })
          
          // Check if form validation caught the invalid phone
          cy.url().then((url) => {
            if (url.includes('/contact-us')) {
              cy.log('✅ Form caught invalid phone - stayed on contact page')
            } else {
              cy.log('⚠️ Form accepted invalid phone - redirected to: ' + url)
            }
          })
          
          return false // Break out of the each loop
        }
      })
      
      if (!contactFormFound) {
        cy.log('❌ No contact form found on this page')
        // Take a screenshot for debugging
        cy.screenshot('no-contact-form-found')
      }
    })
  })

  it('should check for form validation messages', () => {
    // Look for any validation-related text on the page after form submission attempts
    cy.get('form').first().within(() => {
      // Try to submit empty form
      cy.get('input[type="submit"], button[type="submit"]').first().click()
    })
    
    cy.wait(2000)
    
    // Look for common validation message patterns
    const validationPatterns = [
      'required',
      'Please',
      'field',
      'enter',
      'valid',
      'error',
      'missing',
      'complete'
    ]
    
    validationPatterns.forEach(pattern => {
      cy.get('body').then(($body) => {
        if ($body.text().toLowerCase().includes(pattern.toLowerCase())) {
          cy.log(`✅ Found validation text containing: "${pattern}"`)
        }
      })
    })
    
    cy.screenshot('validation-check')
  })
})
