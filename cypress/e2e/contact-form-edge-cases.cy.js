describe('Ryman Healthcare Contact Form - Edge Cases and Boundary Tests', () => {
  let testData

  before(() => {
    cy.fixture('testData').then((data) => {
      testData = data
    })
  })

  beforeEach(() => {
    cy.visitContactForm()
  })

  describe('Boundary Value Testing', () => {
    it('should handle minimum length inputs', () => {
      cy.fillFormPartially({
        firstName: 'A',
        lastName: 'B',
        email: '<EMAIL>',
        phone: '021234567',
        message: 'Hi'
      })
      
      cy.attemptFormSubmission()
      
      // Check if minimum length values are accepted or rejected appropriately
      cy.url().should('include', '/contact-us')
    })

    it('should handle maximum length inputs', () => {
      const maxLengthText = 'A'.repeat(255)
      const longEmail = 'a'.repeat(50) + '@' + 'b'.repeat(50) + '.com'
      
      cy.fillFormPartially({
        firstName: maxLengthText,
        lastName: maxLengthText,
        email: longEmail,
        phone: '021234567890123',
        message: 'A'.repeat(1000)
      })
      
      cy.attemptFormSubmission()
      
      // Form should handle long inputs gracefully
      cy.url().should('include', '/contact-us')
    })

    it('should handle empty spaces in required fields', () => {
      cy.fillFormPartially({
        firstName: '   ',
        lastName: '   ',
        email: '   ',
        phone: '   ',
        message: '   '
      })
      
      cy.attemptFormSubmission()
      
      // Should treat spaces as empty and show validation errors
      cy.url().should('include', '/contact-us')
      cy.get('body').should('contain.text', 'required')
        .or('contain.text', 'Please')
    })
  })

  describe('Special Character Testing', () => {
    it('should handle Unicode characters in name fields', () => {
      cy.fillFormPartially({
        firstName: 'José',
        lastName: 'Müller',
        email: '<EMAIL>',
        phone: '021234567'
      })
      
      cy.attemptFormSubmission()
      
      // Should accept international characters
      cy.url().should('include', '/contact-us')
    })

    it('should handle special characters in message field', () => {
      cy.fillFormPartially({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '021234567',
        message: 'Test message with special chars: !@#$%^&*()_+-=[]{}|;:,.<>?'
      })
      
      cy.attemptFormSubmission()
      
      cy.url().should('include', '/contact-us')
    })

    it('should prevent XSS attempts in form fields', () => {
      const xssPayloads = [
        '<script>alert("xss")</script>',
        'javascript:alert("xss")',
        '<img src="x" onerror="alert(1)">',
        '"><script>alert("xss")</script>'
      ]
      
      xssPayloads.forEach((payload) => {
        cy.fillFormPartially({
          firstName: payload,
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '021234567',
          message: payload
        })
        
        cy.attemptFormSubmission()
        
        // Ensure no script execution
        cy.window().then((win) => {
          expect(win.alert).to.not.have.been.called
        })
        
        cy.url().should('include', '/contact-us')
      })
    })
  })

  describe('Network and Performance Edge Cases', () => {
    it('should handle slow network conditions', () => {
      // Simulate slow network
      cy.intercept('POST', '**/contact**', (req) => {
        req.reply((res) => {
          res.delay(5000) // 5 second delay
          res.send({ statusCode: 200 })
        })
      }).as('slowSubmission')
      
      cy.fillFormPartially(testData.validData)
      cy.attemptFormSubmission()
      
      // Should show loading state or prevent multiple submissions
      cy.get('button[type="submit"]').should('be.disabled')
        .or('contain.text', 'Sending')
        .or('contain.text', 'Please wait')
    })

    it('should handle form submission failures gracefully', () => {
      // Simulate server error
      cy.intercept('POST', '**/contact**', {
        statusCode: 500,
        body: { error: 'Server error' }
      }).as('failedSubmission')
      
      cy.fillFormPartially(testData.validData)
      cy.attemptFormSubmission()
      
      // Should show error message and allow retry
      cy.get('body').should('contain.text', 'error')
        .or('contain.text', 'try again')
        .or('contain.text', 'failed')
    })
  })

  describe('Browser Compatibility Edge Cases', () => {
    it('should handle disabled JavaScript gracefully', () => {
      // This test would need to be run with JS disabled
      // For now, we'll test that form has proper fallbacks
      cy.get('form').should('have.attr', 'action')
      cy.get('form').should('have.attr', 'method')
    })

    it('should work with browser autofill', () => {
      // Test that form works with browser autofill
      cy.get('input[name*="firstname"]').should('have.attr', 'autocomplete')
        .or('have.attr', 'name')
      cy.get('input[type="email"]').should('have.attr', 'autocomplete')
        .or('have.attr', 'name')
      cy.get('input[type="tel"]').should('have.attr', 'autocomplete')
        .or('have.attr', 'name')
    })
  })

  describe('Mobile and Responsive Edge Cases', () => {
    it('should handle form on mobile viewport', () => {
      cy.viewport('iphone-x')
      cy.visitContactForm()
      
      // Check that form is usable on mobile
      cy.get('form').should('be.visible')
      cy.get('input, select, textarea').each(($el) => {
        cy.wrap($el).should('be.visible')
      })
    })

    it('should handle form on tablet viewport', () => {
      cy.viewport('ipad-2')
      cy.visitContactForm()
      
      // Check that form is usable on tablet
      cy.get('form').should('be.visible')
      cy.get('button[type="submit"]').should('be.visible')
    })
  })

  describe('Session and State Edge Cases', () => {
    it('should handle page refresh with form data', () => {
      cy.fillFormPartially({
        firstName: 'John',
        lastName: 'Doe'
      })
      
      cy.reload()
      
      // Check if form data is preserved or cleared appropriately
      cy.get('input[name*="firstname"]').should('have.value', '')
    })

    it('should handle browser back button after form interaction', () => {
      cy.fillFormPartially({
        firstName: 'John',
        lastName: 'Doe'
      })
      
      cy.visit('/') // Navigate away
      cy.go('back') // Go back to form
      
      // Form should be in a clean state
      cy.get('input[name*="firstname"]').should('have.value', '')
    })

    it('should handle multiple tabs with the same form', () => {
      // This is more of a conceptual test as Cypress runs in single tab
      cy.fillFormPartially({
        firstName: 'John'
      })
      
      // Simulate what would happen with multiple tabs
      cy.window().then((win) => {
        // Check that form state is independent
        expect(win.localStorage.getItem('formData')).to.be.null
      })
    })
  })
})
