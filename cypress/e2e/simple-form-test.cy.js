describe('Ryman Healthcare Contact Form - Simple Test', () => {
  it('should find and test any contact form on the page', () => {
    cy.visit('https://www.rymanhealthcare.co.nz/contact-us?hsCtaTracking=0d478680-803f-47ee-ba44-8f08f5539fb4%7Ca9f3e1bd-9b32-4579-9fd1-28ea95b44dce')

    // Wait for page to load
    cy.wait(5000)

    // Look for any form elements
    cy.get('body').then(($body) => {
      // Check if there are any forms
      if ($body.find('form').length > 0) {
        cy.log('Found traditional HTML forms')
        cy.get('form').should('exist')

        // Try to find and test form fields
        cy.get('form').first().within(() => {
          // Look for any input fields
          cy.get('input').then(($inputs) => {
            if ($inputs.length > 0) {
              cy.log(`Found ${$inputs.length} input fields in form`)

              // Try to fill the first few inputs with test data
              $inputs.each((index, input) => {
                if (index < 3) { // Only test first 3 inputs
                  const $input = Cypress.$(input)
                  const type = $input.attr('type') || 'text'

                  if (type === 'email') {
                    cy.wrap($input).type('<EMAIL>')
                  } else if (type === 'tel' || type === 'phone') {
                    cy.wrap($input).type('021234567')
                  } else if (type === 'text') {
                    cy.wrap($input).type('Test Name')
                  }
                }
              })

              // Try to submit the form (should fail with validation)
              cy.get('button[type="submit"], input[type="submit"]').first().click()

              // Check if we're still on the same page (form validation prevented submission)
              cy.url().should('include', '/contact-us')

            } else {
              cy.log('No input fields found in form')
            }
          })
        })

      } else {
        cy.log('No traditional HTML forms found, checking for embedded forms')

        // Look for HubSpot forms or other embedded forms
        cy.get('body').then(($body) => {
          const hubspotSelectors = [
            '.hs-form',
            '[class*="hs-form"]',
            '#hsForm',
            '[id*="hsForm"]',
            '.hbspt-form'
          ]

          let foundHubSpot = false
          hubspotSelectors.forEach(selector => {
            if ($body.find(selector).length > 0) {
              foundHubSpot = true
              cy.log(`Found HubSpot form with selector: ${selector}`)

              // Test HubSpot form
              cy.get(selector).within(() => {
                // Look for input fields
                cy.get('input').then(($inputs) => {
                  if ($inputs.length > 0) {
                    cy.log(`Found ${$inputs.length} inputs in HubSpot form`)

                    // Try to test form validation by submitting empty form
                    cy.get('input[type="submit"], button[type="submit"]').first().click()

                    // Should show validation errors
                    cy.get('body').should('contain.text', 'required')
                      .or('contain.text', 'Please')
                      .or('contain.text', 'field')
                  }
                })
              })
            }
          })

          if (!foundHubSpot) {
            cy.log('No HubSpot forms found either')

            // Look for any iframe that might contain a form
            if ($body.find('iframe').length > 0) {
              cy.log('Found iframes, forms might be embedded')
              cy.get('iframe').should('exist')
            } else {
              cy.log('No forms found on this page')
              // This might mean the form is loaded via JavaScript or is on a different page

              // Let's check if there are any links to contact forms
              cy.get('a[href*="contact"], a[href*="enquiry"], a[href*="form"]').then(($links) => {
                if ($links.length > 0) {
                  cy.log(`Found ${$links.length} links that might lead to contact forms`)
                  // Click the first contact link
                  cy.wrap($links.first()).click()
                  cy.wait(3000)

                  // Now check for forms on the new page
                  cy.get('form').then(($forms) => {
                    if ($forms.length > 0) {
                      cy.log('Found form after following contact link')
                      // Test empty form submission
                      cy.get('form').first().within(() => {
                        cy.get('input[type="submit"], button[type="submit"]').first().click()
                      })
                      cy.get('body').should('contain.text', 'required')
                        .or('contain.text', 'Please')
                    }
                  })
                }
              })
            }
          }
        })
      }
    })

    // Take a screenshot regardless
    cy.screenshot('contact-page-analysis')
  })
})
