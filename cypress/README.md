# Ryman Healthcare Contact Form - Cypress Test Suite

This test suite contains comprehensive negative test cases for the "Contact us with your enquiry" form on the Ryman Healthcare website.

## Test Coverage

### 1. Negative Test Cases (`contact-form-negative-tests.cy.js`)
- **Empty Form Submission**: Tests form behavior when submitted without any data
- **Invalid Email Format**: Tests various invalid email formats
- **Invalid Phone Number**: Tests various invalid phone number formats  
- **Partial Form Completion**: Tests incomplete form submissions
- **Missing Enquiry Type**: Tests submission without selecting enquiry type
- **Field Length Validation**: Tests extremely long inputs and special characters

### 2. Field Validation Tests (`contact-form-field-validation.cy.js`)
- **Individual Field Validation**: Tests each field's validation rules
- **Cross-Field Validation**: Tests validation across multiple fields
- **Form State Validation**: Tests form state preservation and error clearing
- **Accessibility Validation**: Tests ARIA labels and screen reader compatibility

### 3. Edge Cases and Boundary Tests (`contact-form-edge-cases.cy.js`)
- **Boundary Value Testing**: Tests minimum/maximum length inputs
- **Special Character Testing**: Tests Unicode, XSS prevention
- **Network Edge Cases**: Tests slow network and server errors
- **Browser Compatibility**: Tests autofill and JavaScript fallbacks
- **Mobile/Responsive**: Tests on different viewport sizes
- **Session/State**: Tests page refresh and navigation scenarios

## Test Data

Test data is stored in `cypress/fixtures/testData.json` and includes:
- Valid form data examples
- Invalid email formats
- Invalid phone number formats
- Partial form completion scenarios
- Expected error messages

## Running the Tests

### Prerequisites
```bash
npm install
```

### Run All Contact Form Tests
```bash
npm run test:contact-form
```

### Run Specific Test Suites
```bash
# Negative test cases only
npm run test:negative

# Field validation tests only
npm run test:validation

# Edge cases and boundary tests only
npm run test:edge-cases
```

### Interactive Mode
```bash
npm run cypress:open
```

### Headless Mode
```bash
npm run cypress:run
```

## Test Strategy

### Focus on Negative Testing
All tests are designed to validate that the form properly handles:
- Incomplete inputs
- Invalid data formats
- Edge cases and boundary conditions
- Security vulnerabilities (XSS prevention)
- Accessibility requirements

### Test Approach
1. **Form Field Discovery**: Tests use flexible selectors to find form fields
2. **Validation Error Detection**: Tests look for common validation patterns
3. **State Preservation**: Tests verify form maintains state during validation
4. **Cross-Browser Compatibility**: Tests include viewport and responsive checks

## Custom Commands

The test suite includes custom Cypress commands in `cypress/support/commands.js`:

- `cy.visitContactForm()`: Navigates to the contact form page
- `cy.fillFormPartially(data)`: Fills form fields with provided data
- `cy.attemptFormSubmission()`: Attempts to submit the form
- `cy.checkValidationError(selector, error)`: Checks for validation errors

## Expected Behavior

### Form Should Prevent Submission When:
- Required fields are empty
- Email format is invalid
- Phone number format is invalid
- Enquiry type is not selected
- Input contains malicious scripts

### Form Should Show Validation Errors:
- Immediately after field loses focus (real-time validation)
- After form submission attempt
- With appropriate ARIA attributes for accessibility

### Form Should Handle Gracefully:
- Network failures
- Server errors
- Long input values
- Special characters
- Multiple submission attempts

## Test Configuration

Tests are configured in `cypress.config.js` with:
- Base URL: `https://www.rymanhealthcare.co.nz`
- Viewport: 1280x720 (with mobile/tablet tests)
- Timeouts: 10 seconds for commands and requests
- Video recording enabled for failed tests
- Screenshots on test failure

## Maintenance Notes

### Updating Selectors
If the form structure changes, update selectors in:
- `cypress/support/commands.js` (custom commands)
- Individual test files

### Adding New Test Cases
1. Add test data to `cypress/fixtures/testData.json`
2. Create new test cases following existing patterns
3. Use descriptive test names and comments

### Debugging Failed Tests
1. Check screenshots in `cypress/screenshots/`
2. Review videos in `cypress/videos/`
3. Use `cy.debug()` or `cy.pause()` for interactive debugging

## Browser Support

Tests are designed to work across modern browsers:
- Chrome (default)
- Firefox
- Edge
- Safari (with limitations)

## Reporting

Test results include:
- Pass/fail status for each test
- Screenshots of failures
- Video recordings of test runs
- Detailed error messages and stack traces
