#!/bin/bash

# Ryman Healthcare Contact Form Test Runner
# This script runs the Cypress test suite for negative test cases

echo "🧪 Ryman Healthcare Contact Form - Negative Test Suite"
echo "=================================================="
echo ""

# Check if Node.js and npm are installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    echo ""
fi

# Function to run specific test suite
run_test_suite() {
    local test_name=$1
    local test_command=$2
    
    echo "🔍 Running $test_name..."
    echo "Command: $test_command"
    echo ""
    
    if eval $test_command; then
        echo "✅ $test_name completed successfully"
    else
        echo "❌ $test_name failed"
        return 1
    fi
    echo ""
}

# Main menu
echo "Select test suite to run:"
echo "1. All Contact Form Tests"
echo "2. Negative Test Cases Only"
echo "3. Field Validation Tests Only"
echo "4. Edge Cases and Boundary Tests Only"
echo "5. Interactive Mode (Cypress GUI)"
echo "6. Run All Tests in Sequence"
echo ""

read -p "Enter your choice (1-6): " choice

case $choice in
    1)
        run_test_suite "All Contact Form Tests" "npm run test:contact-form"
        ;;
    2)
        run_test_suite "Negative Test Cases" "npm run test:negative"
        ;;
    3)
        run_test_suite "Field Validation Tests" "npm run test:validation"
        ;;
    4)
        run_test_suite "Edge Cases and Boundary Tests" "npm run test:edge-cases"
        ;;
    5)
        echo "🖥️  Opening Cypress in interactive mode..."
        npm run cypress:open
        ;;
    6)
        echo "🔄 Running all test suites in sequence..."
        echo ""
        
        run_test_suite "Negative Test Cases" "npm run test:negative"
        if [ $? -eq 0 ]; then
            run_test_suite "Field Validation Tests" "npm run test:validation"
            if [ $? -eq 0 ]; then
                run_test_suite "Edge Cases and Boundary Tests" "npm run test:edge-cases"
                if [ $? -eq 0 ]; then
                    echo "🎉 All test suites completed successfully!"
                else
                    echo "❌ Edge Cases test suite failed"
                fi
            else
                echo "❌ Field Validation test suite failed"
            fi
        else
            echo "❌ Negative Test Cases suite failed"
        fi
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again and select 1-6."
        exit 1
        ;;
esac

echo ""
echo "📊 Test Results:"
echo "- Check cypress/screenshots/ for failure screenshots"
echo "- Check cypress/videos/ for test recordings"
echo "- Review test reports in the terminal output above"
echo ""
echo "📚 For more information, see cypress/README.md"
